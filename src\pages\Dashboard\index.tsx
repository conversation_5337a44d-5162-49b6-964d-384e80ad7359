'use client';

import React, { useEffect, useState, useMemo, useRef } from 'react';
import './index.less';
import { SearchOutlined, InfoCircleOutlined, DeleteOutlined, MoreOutlined, EditOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { ThemeConfigValues } from '@/components/ChartSettings';
import { Button, Input, Tooltip, Popconfirm, Dropdown, Modal, App } from 'antd';
import ReactEcharts from 'echarts-for-react';
import type { EChartsCoreOption, EChartsType } from 'echarts/core';
import type { SeriesOption, BarSeriesOption, LineSeriesOption, PieSeriesOption, ScatterSeriesOption } from 'echarts/types/dist/shared';
import { applyChartSettings } from '@/utils/chartSettings';
import ChartContainer from './components/Charts/ChartContainer';
import AddChartModal from './components/AddChartModal';
import AddDashboardModal from '@/components/AddDashboardModal';
import {
  listChartByDashboardId,
  listAllDashboard,
  deleteDashboard,
  getDashboardById,
  getChartDataById,
  getDatasourceById,
  editChart,
} from '@/services/DataLoom/yibiaopanjiekou';
import 'echarts-wordcloud';

interface chartType {
  id: string;
  chartName: string;
  chartType: string;
  component: React.ReactElement;
  chartOption: any;
  analysisLastFlag: boolean;
  analysisRes?: string;
  dataOption?: any;
  chartStyle?: any;
}

interface ChartTypeConfig {
  id: string;
  name: string;
  icon: string;
}

// 图表类型配置数组
const chartTypes: ChartTypeConfig[] = [
  { id: 'line', name: '折线图', icon: '/assets/9htgl43h.svg' },
  { id: 'bar', name: '柱状图', icon: '/assets/mnssb39i.svg' },
  { id: 'horizontal-bar', name: '条形图', icon: '/assets/jybw14mr.svg' },
  { id: 'scatter', name: '散点图', icon: '/assets/udgwbutc.svg' },
  { id: 'funnel', name: '漏斗图', icon: '/assets/jeejfyi6.svg' },
  { id: 'word-cloud', name: '词云', icon: '/assets/2r90fnlb.svg' },
  { id: 'pie', name: '饼图', icon: '/assets/acprqf2i.svg' },
  { id: 'combo', name: '组合图', icon: '/assets/7si8jsd7.svg' },
];

const DataDashboard: React.FC = () => {
  const { message } = App.useApp();
  const navigate = useNavigate();
  const [datasourceId, setDatasourceId] = useState<string>('');
  const [datasourceType, setDatasourceType] = useState<string>('');
  const [configuration, setConfiguration] = useState<string>('');
  const [dashboardId, setDashboardId] = useState<string>('');
  const [isAnalyzeEnabled, setIsAnalyzeEnabled] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState<boolean>(false);
  const [searchKeyword, setSearchKeyword] = useState<string>('');

  const [addChartModalVisible, setAddChartModalVisible] = useState(false);
  const [selectedChart, setSelectedChart] = useState<chartType | null>(null);
  const [addDashboardModalVisible, setAddDashboardModalVisible] = useState(false);
  const [menuOptions, setMenuOptions] = useState<any[]>([]);

  const [layouts, setLayouts] = useState(undefined);
  const [charts, setCharts] = useState<chartType[]>([]);
  const renderCharts = useMemo(() => {
    // 创建固定3个位置的数组，不足的用null填充
    const fixedCharts = new Array(3).fill(null);
    charts.forEach((chart, index) => {
      if (index < 3) {
        fixedCharts[index] = chart;
      }
    });
    return fixedCharts;
  }, [charts]);

  const chartRef = useRef<any>(null);

  const [isRightPanelCollapsed, setIsRightPanelCollapsed] = useState(false);

  const updateChartOption = (newSettings: ThemeConfigValues) => {
    if (!selectedChart) return;

    const currentOption = selectedChart.chartOption;

    // 使用封装的工具函数应用设置
    const newOption = applyChartSettings(currentOption, newSettings, selectedChart.chartType);

    setSelectedChart((prev) =>
      prev
        ? {
            ...prev,
            chartOption: newOption,
            chartStyle: newSettings,
          }
        : null,
    );
  };

  const handleSettingsChange = (settings: ThemeConfigValues) => {
    updateChartOption(settings);
  };

  const handleAddChart = () => {
    setAddChartModalVisible(false);
    loadCharts();
  };

  const handleChartClick = (chartId: string) => {
    const chart = charts.find((c) => c.id === chartId);
    setSelectedChart(chart || null);
  };

  const handleInfoClick = () => {
    setAddDashboardModalVisible(true);
  };

  const handleDeleteDashboard = async (dashboardId: number) => {
    Modal.confirm({
      title: '删除确认',
      content: '确定要删除这个仪表盘吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const res = await deleteDashboard({ dashboardId });
          if (res.code === 0) {
            message.success('删除成功');
            loadAllDashboard();
          } else {
            message.error(res.message || '删除失败');
          }
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  const handleSaveDashboard = async () => {
    if (!selectedChart) return;

    try {
      const params = {
        dashboardId: dashboardId,
        chartName: selectedChart.chartName,
        chartOption: JSON.stringify(selectedChart.chartOption),
        dataOption: JSON.stringify(selectedChart.dataOption),
        chartStyle: JSON.stringify(selectedChart.chartStyle),
        chartType: selectedChart.chartType,
        id: selectedChart.id,
      };
      const res = await editChart(params);
      if (res.code === 0) {
        message.success('保存成功');
        loadCharts();
      } else {
        message.error(res.message || '保存失败');
      }
    } catch (error) {
      message.error('保存失败');
    }
  };

  const handleShareDashboard = () => {
    // 分享仪表盘功能
    message.info('分享仪表盘功能开发中...');
  };

  const handleExportDashboard = () => {
    // 导出仪表盘功能
    message.info('导出仪表盘功能开发中...');
  };

  const loadAllDashboard = () => {
    listAllDashboard().then((res) => {
      const { code, data } = res;
      if (code === 0) {
        setMenuOptions(data || []);
        if (data && data.length > 0) {
          setDashboardId(String(data[0]?.id) || '');
          setDatasourceId(String(data[0]?.datasourceId) || '');
        }
      } else {
        message.error(res.message || '获取仪表盘失败');
      }
    });
  };
  const loadCharts = () => {
    if (!dashboardId) {
      return;
    }
    listChartByDashboardId({ dashboardId: dashboardId }).then((res) => {
      const { code, data } = res;
      if (code === 0) {
        Promise.all(
          data.map(async (item: any) => {
            // 处理图表数据
            const processChartData = async (chartItem: any) => {
              if (chartItem.dataType === 'ai') {
                return {
                  ...chartItem,
                  chartOption: chartItem.chartOption
                    ? {
                        ...JSON.parse(chartItem.chartOption),
                        grid: {
                          bottom: 0,
                          left: 0,
                          right: 0,
                          containLabel: true,
                        },
                      }
                    : {},
                };
              }

              const chartDataRes = await getChartDataById({ chartId: chartItem.id });
              const chartOption = chartItem.chartOption ? JSON.parse(chartItem.chartOption) : {};
              if (chartDataRes.code !== 0) {
                message.error(chartDataRes.message || '获取图表数据失败');
                // 设置一个默认的空图表配置
                const defaultChartOption = {
                  ...chartOption,
                  legend: {
                    data: chartOption.series?.map((series: any) => series.name) || [],
                  },
                };
                return {
                  ...chartItem,
                  chartOption: defaultChartOption,
                };
              }

              if (
                !chartDataRes.data ||
                !('seriesDataList' in chartDataRes.data) ||
                !('xarrayData' in chartDataRes.data) ||
                !Array.isArray(chartDataRes.data.seriesDataList) ||
                !Object.keys(chartDataRes.data.xarrayData || {}).length
              ) {
                return {
                  ...chartItem,
                  chartOption,
                };
              }

              const { seriesDataList, xarrayData } = chartDataRes.data;

              // 根据图表类型处理数据
              switch (chartItem.chartType) {
                case 'horizontal-bar':
                  // 水平条形图
                  const horizontalBarOption = {
                    ...chartOption,
                    xAxis: {
                      type: 'value',
                      show: true,
                      axisLine: { show: true },
                      axisTick: { show: true },
                      splitLine: { show: false },
                    },
                    yAxis: {
                      type: 'category',
                      show: true,
                      axisLine: { show: true },
                      axisTick: { show: true },
                      data: xarrayData?.values || [],
                    },
                    series: seriesDataList.map((series: any) => ({
                      ...chartOption.series?.[0],
                      name: series.title,
                      data: series.data,
                      coordinateSystem: 'cartesian2d',
                      encode: { y: 0, x: 1 },
                    })),
                  };
                  Object.assign(chartOption, horizontalBarOption);
                  break;

                case 'pie':
                  // 饼图
                  if (seriesDataList[0]?.data) {
                    const pieOption = {
                      ...chartOption,
                      series: [
                        {
                          ...chartOption.series?.[0],
                          data: seriesDataList[0].data.map((value: number, index: number) => ({
                            name: xarrayData?.values?.[index] || `类别${index + 1}`,
                            value: value,
                          })),
                        },
                      ],
                    };
                    Object.assign(chartOption, pieOption);
                  }
                  break;

                case 'scatter':
                  // 散点图
                  const scatterOption = {
                    ...chartOption,
                    xAxis: {
                      type: 'value',
                      show: true,
                      axisLine: { show: true },
                      axisTick: { show: true },
                      splitLine: { show: false },
                    },
                    yAxis: {
                      type: 'value',
                      show: true,
                      axisLine: { show: true },
                      axisTick: { show: true },
                      splitLine: { show: true },
                    },
                    series: seriesDataList.map((series: any) => ({
                      ...chartOption.series?.[0],
                      name: series.title,
                      data: series.data.map((value: number, index: number) => [index, value, value]),
                    })),
                  };
                  Object.assign(chartOption, scatterOption);
                  break;

                case 'word-cloud':
                  // 词云图
                  if (seriesDataList[0]?.data) {
                    const wordCloudOption = {
                      ...chartOption,
                      series: [
                        {
                          ...chartOption.series?.[0],
                          data: seriesDataList[0].data.map((value: number, index: number) => ({
                            name: xarrayData?.values?.[index] || `词${index + 1}`,
                            value: value,
                            textStyle: {
                              normal: {
                                color: `rgb(${Math.random() * 255},${Math.random() * 255},${Math.random() * 255})`,
                              },
                            },
                          })),
                        },
                      ],
                    };
                    Object.assign(chartOption, wordCloudOption);
                  }
                  break;

                default:
                  // 其他图表类型（柱状图、折线图等）
                  if (xarrayData && 'values' in xarrayData && 'seriesDataList' in chartDataRes.data) {
                    const defaultOption = {
                      ...chartOption,
                      xAxis: {
                        type: 'category',
                        show: true,
                        axisLine: { show: true },
                        axisTick: { show: true },
                        splitLine: { show: false },
                        data: xarrayData.values,
                      },
                      yAxis: {
                        type: 'value',
                        show: true,
                        axisLine: { show: true },
                        axisTick: { show: true },
                        splitLine: { show: true },
                      },
                      legend: {
                        data: seriesDataList.map((series: any) => series.title) || [],
                      },
                      series: seriesDataList.map((series: any) => ({
                        ...chartOption.series?.[0],
                        name: series.title,
                        data: series.data,
                      })),
                    };
                    Object.assign(chartOption, defaultOption);
                  }
              }

              return {
                ...chartItem,
                chartOption,
                chartData: chartDataRes.data,
              };
            };

            return processChartData(item);
          }),
        )
          .then((charts) => {
            console.log(charts);
            setCharts(charts);
          })
          .catch((error) => {
            message.error('获取图表数据失败');
            console.error('获取图表数据失败:', error);
          });
      }
    });
  };

  const loadDatasource = () => {
    const params = {
      datasourceId,
    };
    getDatasourceById(params).then((res) => {
      const { code, data } = res;
      if (code === 0) {
        setDatasourceType(data?.type || '');
        setConfiguration(data?.configuration || '');
      }
    });
  };

  useEffect(() => {
    loadAllDashboard();
  }, []);

  useEffect(() => {
    if (dashboardId) {
      loadCharts();
    }
  }, [dashboardId]);
  useEffect(() => {
    if (datasourceId) {
      loadDatasource();
    }
  }, [datasourceId]);

  const getChartOption = useMemo(() => {
    return (chart: chartType) => {
      if (selectedChart?.id === chart.id) {
        return selectedChart.chartOption;
      }
      return chart.chartOption;
    };
  }, [selectedChart]);

  const filteredMenuOptions = useMemo(() => {
    if (!searchKeyword) return menuOptions;
    return menuOptions.filter((option) => option.name.toLowerCase().includes(searchKeyword.toLowerCase()));
  }, [menuOptions, searchKeyword]);

  return (
    <div className="dashboard-wrapper">
      {menuOptions.length > 0 ? (
        <div className="dashboard-container">
          <div className={`sidebar ${isSidebarCollapsed ? 'collapsed' : ''}`}>
            <div className="sidebar-header">
              <span>仪表盘</span>
              <div className="header-actions">
                <img src="/assets/image_1752825292180_px0q6p.svg" alt="info" onClick={handleInfoClick} />
                <img
                  src={isSidebarCollapsed ? '/assets/t5hbz8e9.svg' : '/assets/r2l6w3pe.svg'}
                  alt="collapse"
                  className="collapse-icon"
                  onClick={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
                />
              </div>
            </div>
            <div className="search-box">
              {!isSidebarCollapsed ? (
                <Input
                  type="text"
                  placeholder="输入关键词搜索"
                  allowClear
                  suffix={<SearchOutlined />}
                  value={searchKeyword}
                  onChange={(e) => setSearchKeyword(e.target.value)}
                />
              ) : (
                <div className="collapsed-icons">
                  <img src="/assets/t5hbz8e9.svg" alt="expand" className="icon" onClick={() => setIsSidebarCollapsed(false)} />
                  <img src="/assets/cria3xom.svg" alt="add" className="icon" onClick={() => setAddChartModalVisible(true)} />
                  <img
                    src="/assets/jbq9a3gu.svg"
                    alt="search"
                    style={{ marginLeft: '3px' }}
                    className="icon"
                    onClick={() => setIsSidebarCollapsed(false)}
                  />
                </div>
              )}
            </div>
            {!isSidebarCollapsed && (
              <div className="menu">
                {filteredMenuOptions.map((option, index) => (
                  <div
                    key={`menu-item-${option.id}-${index}`}
                    className={`menu-item ${dashboardId === option.id ? 'active' : ''}`}
                    onClick={() => {
                      setDashboardId(option.id);
                      setDatasourceId(option.datasourceId);
                    }}
                    title={option.name}
                  >
                    <div className="menu-item-container">
                      <img src="/assets/image_1752825291616_d1yw6f.svg" alt="info" />
                      <span className="menu-item-name">{option.name}</span>
                    </div>
                    <Dropdown
                      key={`dropdown-${option.id}-${index}`}
                      menu={{
                        items: [
                          {
                            key: `delete-${option.id}-${index}`,
                            icon: <DeleteOutlined />,
                            label: '删除',
                            danger: true,
                            onClick: () => {
                              handleDeleteDashboard(option.id);
                            },
                          },
                        ],
                      }}
                      trigger={['click']}
                    >
                      <Button type="text" icon={<MoreOutlined />} className="more-button" onClick={(e) => e.stopPropagation()} />
                    </Dropdown>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="main-content">
            {(dashboardId || selectedChart || isRightPanelCollapsed) && (
              <div className="action-buttons">
                <div className="action-buttons-left">
                  {dashboardId && (
                    <Button type="primary" className="add-chart-btn" onClick={() => setAddChartModalVisible(true)}>
                      添加图表
                    </Button>
                  )}
                  {selectedChart && (
                    <Button className="save-btn" onClick={handleSaveDashboard}>
                      保存仪表盘
                    </Button>
                  )}
                </div>
                <div className="action-buttons-right">
                  {/* {dashboardId && (
                    <div className="ai-assistant-btn">
                      <img src="/assets/image_1753151211856_cc3fyb.svg" alt="AI助手" />
                    </div>
                  )}
                  {dashboardId && (
                    <Button className="share-btn" onClick={handleShareDashboard}>
                      <img
                        src="/assets/image_1753151212975_tjrf8x.svg"
                        alt="分享"
                        style={{ width: '14px', height: '14px', marginRight: '4px' }}
                      />
                      分享仪表盘
                    </Button>
                  )} */}
                  {isRightPanelCollapsed && (
                    <img
                      src="/assets/t5hbz8e9.svg"
                      alt="collapse"
                      className="collapse-icon"
                      onClick={() => setIsRightPanelCollapsed(!isRightPanelCollapsed)}
                    />
                  )}
                </div>
              </div>
            )}
            <div className="dashboard-grid-container">
              {dashboardId ? (
                <div className="dashboard-grid">
                  {renderCharts.map((chart, index) => (
                    <div
                      key={`chart-slot-${index}`}
                      className={`chart-container ${chart && selectedChart?.id === chart.id ? 'selected' : ''} ${!chart ? 'empty-slot' : ''}`}
                      onClick={() => (chart ? handleChartClick(chart.id) : setAddChartModalVisible(true))}
                    >
                      {chart ? (
                        <ChartContainer
                          key={`chart-container-${chart.id}`}
                          title={chart.chartName}
                          chartOption={getChartOption(chart)}
                          chartId={chart.id}
                          dashboardId={dashboardId}
                          chartName={chart.chartName}
                          showAnalyze={chart.analysisRes ? true : false}
                          onAnalyze={() => setIsAnalyzeEnabled(!isAnalyzeEnabled)}
                          onRename={() => loadCharts()}
                          onDelete={() => loadCharts()}
                        >
                          <ReactEcharts
                            key={`echarts-${chart.id}`}
                            option={getChartOption(chart)}
                            style={{ height: '100%', width: '100%' }}
                            opts={{ renderer: 'svg' }}
                            notMerge={true}
                            lazyUpdate={false}
                          />
                        </ChartContainer>
                      ) : (
                        <div className="empty-chart-placeholder">
                          <div className="placeholder-content">
                            <div className="placeholder-icon">+</div>
                            <div className="placeholder-text">添加图表</div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="dashboard-empty-charts">
                  <div className="dashboard-empty-charts-content">
                    <h1 className="dashboard-empty-charts-title">选择组件，开始搭建你的数据看板</h1>
                    <p className="dashboard-empty-charts-description">
                      快速插入柱状图、折线图、饼图等多种组件类型，直观展示数据，让业务动态一目了然
                    </p>
                    {/* <div className="dashboard-empty-charts-button-container">
                      <Button className="dashboard-empty-charts-button primary" onClick={() => setAddChartModalVisible(true)}>
                        <img src="/assets/image_1753151212465_2bgtdx.svg" alt="添加" />
                        AI生成
                      </Button>
                    </div> */}

                    <div className="chart-types-grid">
                      {chartTypes.map((chartType) => (
                        <div key={chartType.id} className="chart-type-item">
                          <div className="chart-icon">
                            <img src={chartType.icon} alt={chartType.name} />
                          </div>
                          <span className="chart-name">{chartType.name}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      ) : (
        <div className="dashboard-empty">
          <div className="dashboard-empty-content">
            <h1 className="dashboard-empty-title">添加图表，开始搭建你的数据看板</h1>
            <p className="dashboard-empty-description">快速插入柱状图、折线图、饼图等多种组件类型，直观展示数据，让业务动态一目了然</p>
            <div className="dashboard-empty-actions">
              <Button className="dashboard-empty-button primary" onClick={() => setAddDashboardModalVisible(true)}>
                添加仪表盘
              </Button>
              <Button className="dashboard-empty-button secondary" onClick={() => navigate('/ai_chat')}>
                查看历史记录
              </Button>
            </div>
          </div>
        </div>
      )}

      <AddChartModal
        visible={addChartModalVisible}
        datasourceType={datasourceType}
        configuration={configuration}
        dashboardId={dashboardId}
        datasourceId={datasourceId}
        onCancel={() => setAddChartModalVisible(false)}
        onConfirm={handleAddChart}
      />
      <AddDashboardModal
        visible={addDashboardModalVisible}
        onCancel={() => setAddDashboardModalVisible(false)}
        onSuccess={() => {
          setAddDashboardModalVisible(false);
          loadAllDashboard();
        }}
      />
    </div>
  );
};

export default DataDashboard;
